import { sqliteTable, text, integer} from 'drizzle-orm/sqlite-core';
export const setting = sqliteTable('setting', {
	register: integer('register').default(0).notNull(),
	receive: integer('receive').default(0).notNull(),
	title: text('title').default('').notNull(),
	manyEmail: integer('many_email').default(1).notNull(),
	addEmail: integer('add_email').default(0).notNull(),
	autoRefreshTime: integer('auto_refresh_time').default(0).notNull(),
	addEmailVerify: integer('add_email_verify').default(1).notNull(),
	registerVerify: integer('register_verify').default(1).notNull(),
	regVerifyCount: integer('reg_verify_count').default(1).notNull(),
	addVerifyCount: integer('add_verify_count').default(1).notNull(),
	send: integer('send').default(1).notNull(),
	r2Domain: text('r2_domain'),
	secretKey: text('secret_key'),
	siteKey: text('site_key'),
	regKey: integer('reg_key').default(1).notNull(),
	background: text('background'),
	tgBotToken: text('tg_bot_token').default('').notNull(),
	tgChatId: text('tg_chat_id').default('').notNull(),
	tgBotStatus: integer('tg_bot_status').default(1).notNull(),
	forwardEmail: text('forward_email').default('').notNull(),
	forwardStatus: integer('forward_status').default(1).notNull(),
	ruleEmail: text('rule_email').default('').notNull(),
	ruleType: integer('rule_type').default(0).notNull(),
	loginOpacity: integer('login_opacity').default(0.88),
	resendTokens: text('resend_tokens').default("{}").notNull(),
	noticeTitle: text('notice_title').default('').notNull(),
	noticeContent: text('notice_content').default('').notNull(),
	noticeType: text('notice_type').default('').notNull(),
	noticeDuration: integer('notice_duration').default(0).notNull(),
	noticePosition: text('notice_position').default('').notNull(),
	noticeOffset: integer('notice_offset').default(0).notNull(),
	noticeWidth: integer('notice_width').default(400).notNull(),
	notice: integer('notice').default(0).notNull(),
	noRecipient: integer('no_recipient').default(1).notNull(),
	loginDomain: integer('login_domain').default(0).notNull()
});
export default setting
