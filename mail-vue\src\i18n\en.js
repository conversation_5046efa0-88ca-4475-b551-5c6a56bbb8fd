const en = {
    inbox: 'Inbox',
    drafts: 'Drafts',
    sent: 'Sent',
    starred: 'Starred',
    settings: 'Settings',
    analytics: 'Analytics',
    allUsers: 'All Users',
    allMail: 'All Mail',
    permissions: 'Role',
    inviteCode: 'Invite Code',
    SystemSettings: 'System Settings',
    noMoreData: 'No more data',
    noMessagesFound: 'No messages found',
    addAccount: 'Add Account',
    emailAccount: 'Email',
    deleteUser: 'Delete Account',
    deleteUserBtn: 'Delete',
    changePassword: 'Change Password',
    newPassword: 'New password',
    confirmPassword: 'Confirm password',
    add: 'Add',
    manage: 'Manage',
    rename: 'Rename',
    delete: 'Delete',
    save: 'Save',
    profile: 'Profile',
    change: 'Change',
    changePwdBtn: 'Change',
    username: 'Username',
    password: 'Password',
    delAccount: 'Delete Account',
    delAccountMsg: 'This will permanently delete your account and data. It cannot be reactivated.',
    totalReceived: 'Total Received',
    totalSent: 'Total Sent',
    totalMailboxes: 'Total Accounts',
    totalUsers: 'Total Users',
    deleted: 'Deleted',
    selectDeleted: 'Deleted',
    active: 'Active',
    emailSource: 'Email Source',
    userGrowth: 'User Growth',
    emailGrowth: 'Email Growth',
    emailSent: 'Sent',
    emailReceived: 'Received',
    sentToday: 'Sent Today',
    total: 'Total',
    growthTotalUsers: 'Total Users',
    searchByEmail: 'Enter email to search',
    tabEmailAddress: 'Email',
    tabReceived: 'Received',
    tabSent: 'Sent',
    tabMailboxes: 'Accounts',
    tabRegisteredAt: 'Registered at',
    tabStatus: 'Status',
    tabRole: 'Role',
    roleName: 'Name',
    role: 'Name',
    all: 'All',
    normal: 'Normal',
    banned: 'Banned',
    reset: 'Reset',
    restore: 'Restore',
    tabSetting: 'Settings',
    registrationIp: 'Registration IP',
    recentIP: 'Recent IP',
    recentActivity: 'Recent Activity',
    loginDevice: 'Login Device',
    loginSystem: 'Login System',
    browserLogin: 'Browser Login',
    unauthorized: 'Unauthorized',
    unlimited: 'Unlimited',
    sendCount: 'Send email : ',
    accountCount: 'Add account : ',
    action: 'Action',
    chgPwd: 'Pwd',
    perm: 'Role',
    btnBan: 'Ban',
    admin: 'Admin',
    addUser: 'Add User',
    select: 'Select',
    unknown: 'Unknown',
    changePerm: 'Change Role',
    from: 'From',
    subject: 'Subject',
    sender: 'Sender',
    user: 'User',
    searchByContent: 'Enter text to search',
    noRecipient: 'No recipient',
    received: 'Received',
    selectEmail: 'Email',
    order: 'Order',
    default: 'Default',
    description: 'Description',
    removeContent: 'Remove content',
    removeAll: 'Remove all',
    expand: 'Expand',
    collapse: 'Collapse',
    daily: 'Daily',
    searchRegKeyDesc: 'Enter invite code to search',
    remainingUses: 'Remaining Uses',
    exhausted: 'Exhausted',
    validUntil: 'Valid Until',
    expired: 'Expired',
    copy: 'Copy',
    history: 'History',
    addRegKey: 'Add Invite Code',
    regKey: 'Invite Code',
    noCodeFound: 'No messages found',
    useHistory: 'Usage History',
    date: 'Date',
    roleDesc: 'Role',
    noSubject: 'No subject',
    recipient: 'To',
    delivered: 'Delivered',
    complained: 'Complained',
    delayed: 'Delayed',
    bounced: 'Bounced',
    attachments: 'Attachments',
    attCount: 'Total {total}',
    emailCount: 'Total {total}',
    error404: '404 Not Found',
    home: 'Home',
    loginBtn: 'Sign in',
    regBtn: 'Sign up',
    loginTitle: 'Sign in to your account to access email',
    regTitle: 'Sign up for an account to use email',
    confirmPwd: 'Confirm password',
    regKeyOptional: 'Invite code (Optional)',
    noAccount: 'Don\'t have an account? \u200B',
    hasAccount: 'Already have an account? \u200B',
    regSwitch: 'Sign up',
    loginSwitch: 'Sign in',
    websiteSetting: 'Website',
    websiteReg: 'Sign Up',
    loginDomain: 'Sign-In Box Domain',
    multipleEmail: 'Multiple Accounts',
    multipleEmailDesc: 'Enable this feature to allow users to add multiple accounts.',
    physicallyWipeData: 'Physically Wipe Data',
    physicallyWipeDataDesc: 'This action will permanently erase all deleted data.',
    customization: 'Customization',
    websiteTitle: 'Title',
    loginBoxOpacity: 'Login Box Opacity',
    loginBackground: 'Background',
    emailSetting: 'Email',
    receiveEmail: 'Receive Email',
    autoRefresh: 'Auto Refresh',
    autoRefreshDesc: 'Automatically fetch the latest emails from the server.',
    sendEmail: 'Send Email',
    resendToken: 'Resend Token',
    R2OS: 'R2 Object Storage',
    osDomain: 'Domain',
    emailPush: 'Email Push',
    tgBot: 'Telegram Bot',
    disable: 'Disable',
    disabled: 'Disabled',
    otherEmail: 'Forwarding to External Email',
    forwardingRules: 'Forwarding Rules',
    forwardAll: 'All',
    rules: 'Rules',
    turnstileSetting: 'Turnstile',
    signUpVerification: 'Sign Up Verification',
    addEmailVerification: 'Add Account Verification',
    about: 'About',
    version: 'Version',
    community: 'Community',
    changeTitle: 'Change Title',
    addResendTokenDesc: 'Input to add; leave empty to delete.',
    addOsDomain: 'Add Domain',
    domainDesc: 'Domain',
    addTurnstileSecret: 'Add turnstile secret',
    backgroundTitle: 'Change Background',
    tgBotDesc: 'Forward received emails to a Telegram bot',
    tgBotToken: 'Bot token',
    toBotTokenDesc: 'Multiple user chat_ids, separated by commas',
    otherEmailDesc: 'emails can be forwarded to external email, but must be verified via cloudflare.',
    otherEmailInputDesc: 'Separate multiple email addresses with commas.',
    forwardingRulesDesc: 'Rule-based forwarding only forwards emails received by the specified address.',
    ruleEmailsInputDesc: 'Separate multiple email addresses with commas.',
    resendTokenList: 'Token List',
    domain: 'Domain',
    optional: 'Optional',
    subjectInputDesc: 'Please enter the email subject.',
    changeUserName: 'Change Username',
    sendSeparately: 'Separately',
    send: 'Send',
    reply: 'Reply',
    confirm: 'Confirm',
    cancel: 'Cancel',
    delEmailConfirm: 'Confirm deleting this email?',
    delSuccessMsg: 'Deleted successfully',
    emptyEmailMsg: 'Email cannot be empty',
    notEmailMsg: 'Invalid email',
    emptyPwdMsg: 'Password cannot be empty',
    pwdLengthMsg: 'Password must be at least 6 characters',
    confirmPwdFailMsg: 'The two passwords do not match',
    emptyRegKeyMsg: 'Invite code cannot be empty',
    regSuccessMsg: 'Sign up successful',
    copySuccessMsg: 'Copied successfully',
    copyFailMsg: 'Copy failed',
    clearRegKey: 'Confirm clearing all invalid invite codes?',
    clearSuccess: 'Cleared successfully',
    emptyRole: 'Role cannot be empty',
    emptyTimeMsg: 'Valid until time cannot be empty',
    emptyCountMsg: 'Available count cannot be empty',
    addSuccessMsg: 'Addition successful',
    delConfirm: 'Confirm deleting {msg}?',
    emptyRoleNameMsg: 'Role name cannot be empty',
    saveSuccessMsg: 'Saved successfully',
    changeRoleTitle: 'Change Role',
    addRoleTitle: 'Add Role',
    emptyUserNameMsg: 'Name cannot be empty',
    delAccountConfirm: 'Confirm deleting current account and all associated data?',
    clearAllDelConfirm: 'This action is irreversible. Enter <b style="font-weight: bold">DELETE</b> to proceed',
    warning: 'Warning',
    delInputPattern: 'DELETE',
    inputErrorMessage: 'Please enter DELETE to confirm',
    delBackgroundConfirm: 'Confirm deleting this background?',
    enable: 'Enable',
    enabled: 'Enabled',
    reSendConfirm: 'Confirm reset of {msg} send count?',
    reSuccessMsg: 'Reset successful',
    restoreConfirm: 'Confirm restoring {msg}?',
    normalRestore: 'Normal restore',
    allRestore: 'Includes deleted data',
    restoreSuccessMsg: 'Restore successful',
    banRestore: 'Confirm banning {msg}?',
    logOut: 'Sign out',
    clearContentConfirm: 'Are you sure to clear all content?',
    attLimitMsg: 'Attachment size limit: 28MB',
    emptyRecipientMsg: 'Recipient email cannot be empty',
    emptySubjectMsg: 'Subject cannot be empty',
    emptyContentMsg: 'Content cannot be empty',
    noSeparateSendMsg: 'Separate sending does not support attachments yet',
    sendSuccessMsg: 'Send successful',
    sendFailMsg: 'Send failed',
    saveDraftConfirm: 'Save draft?',
    delEmailsConfirm: 'Confirm batch delete these emails?',
    sending: 'Sending email...',
    sendingErrorMsg: 'Sending in progress',
    networkErrorMsg: 'Network error. Check your internet',
    timeoutErrorMsg: 'Timeout. Try again later',
    serverBusyErrorMsg: 'Server busy. Please try again later',
    reqFailErrorMsg: 'Request failed. Try again later',
    message: 'Message',
    language: 'Language',
    totalUserAccount: '{msg}',
    sendBanned: 'Banned',
    wrote: 'wrote',
    support: 'Support',
    supportDesc: 'Buy me tea',
    featDesc: 'Feature Description',
    emailInterception: 'Email Interception',
    emailInterceptionDesc: 'Enter a domain or email address to prevent users from receiving emails from certain websites.',
    availableDomains: 'Available Domains',
    availableDomainsDesc: 'Restrict users to email domains specified. Domains not on the approved list will be blocked from registration, adding email addresses, and sending/receiving emails. If left blank, all domains will be allowed by default.',
    backgroundUrlDesc: 'Image URL',
    localUpload: ' Local upload',
    imageLink: 'Image URL',
    imageLinkErrorMsg: 'Invalid image URL',
    backgroundWarning: 'Image file size affects website load speed.',
    rulesVerify: 'Rules',
    rulesVerifyTitle: 'Trigger After {count} Daily Uses per IP',
    botVerifyMsg: 'Please verify that you are human',
    noticeTitle: 'Notice',
    noticePopup: 'Sign-In Popup',
    icon: 'Icon',
    position: 'Position',
    offset: 'Offset',
    duration: 'Duration',
    topRight: 'Top Right',
    topLeft: 'Top Left',
    bottomRight: 'Bottom Right',
    bottomLeft: 'Bottom Left',
    width: 'Width',
    titleDesc: 'Title',
    noticeContentDesc: 'Notice content supports HTML',
    verifyModuleFailed: 'Verification module failed to load. Please refresh the page',
    popUp: 'Pop Up',
    noRecipientTitle: 'No Recipient',
    noRecipientDesc: 'Emails can be received even without a registered email address.',
    preview: 'Preview',
    help: 'Help',
    document: 'Document'
}

export default en
