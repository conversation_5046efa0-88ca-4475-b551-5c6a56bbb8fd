{"name": "mail-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "remote": "vite --mode remote", "build": "vite build --mode release", "preview": "vite preview"}, "dependencies": {"@iconify/vue": "^4.3.0", "@vueuse/core": "^12.0.0", "axios": "^1.7.8", "compressorjs": "^1.2.1", "date-time-format-timezone": "^1.0.22", "dayjs": "^1.11.13", "dexie": "^4.0.11", "echarts": "^5.6.0", "element-plus": "^2.9.11", "lodash-es": "^4.17.21", "path": "^0.12.7", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "vue": "^3.5.13", "vue-i18n": "^11.1.10", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "less": "^4.2.2", "sass": "^1.82.0", "terser": "^5.39.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "6.3.4"}}