tinymce.Resource.add('tinymce.html-i18n.help-keynav.vi',
'<h1>Bắt đầu điều hướng bàn phím</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Tập trung vào thanh menu</dt>\n' +
  '  <dd>Windows hoặc Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Tập trung vào thanh công cụ</dt>\n' +
  '  <dd>Windows hoặc Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Tập trung vào chân trang</dt>\n' +
  '  <dd>Windows hoặc Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Tập trung vào thông báo</dt>\n' +
  '  <dd>Windows hoặc Linux: Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>Tập trung vào thanh công cụ ngữ cảnh</dt>\n' +
  '  <dd>Windows, Linux hoặc macOS: Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>Điều hướng sẽ bắt đầu từ mục UI đầu tiên. Mục này sẽ được tô sáng hoặc có gạch dưới (nếu là mục đầu tiên trong\n' +
  '  đường dẫn phần tử Chân trang).</p>\n' +
  '\n' +
  '<h1>Di chuyển qua lại giữa các phần UI</h1>\n' +
  '\n' +
  '<p>Để di chuyển từ một phần UI sang phần tiếp theo, ấn <strong>Tab</strong>.</p>\n' +
  '\n' +
  '<p>Để di chuyển từ một phần UI về phần trước đó, ấn <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>Thứ tự <strong>Tab</strong> của các phần UI này như sau:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Thanh menu</li>\n' +
  '  <li>Từng nhóm thanh công cụ</li>\n' +
  '  <li>Thanh bên</li>\n' +
  '  <li>Đường dẫn phần tử trong chân trang</li>\n' +
  '  <li>Nút chuyển đổi đếm chữ ở chân trang</li>\n' +
  '  <li>Liên kết thương hiệu ở chân trang</li>\n' +
  '  <li>Núm điều tác chỉnh kích cỡ trình soạn thảo ở chân trang</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Nếu người dùng không thấy một phần UI, thì có nghĩa phần đó bị bỏ qua.</p>\n' +
  '\n' +
  '<p>Nếu ở chân trang có tính năng tập trung điều hướng bàn phím, mà không có thanh bên nào hiện hữu, thao tác ấn <strong>Shift+Tab</strong>\n' +
  '  sẽ chuyển hướng tập trung vào nhóm thanh công cụ đầu tiên, không phải cuối cùng.</p>\n' +
  '\n' +
  '<h1>Di chuyển qua lại trong các phần UI</h1>\n' +
  '\n' +
  '<p>Để di chuyển từ một phần tử UI sang phần tiếp theo, ấn phím <strong>Mũi tên</strong> tương ứng cho phù hợp.</p>\n' +
  '\n' +
  '<p>Các phím mũi tên <strong>Trái</strong> và <strong>Phải</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>di chuyển giữa các menu trong thanh menu.</li>\n' +
  '  <li>mở menu phụ trong một menu.</li>\n' +
  '  <li>di chuyển giữa các nút trong nhóm thanh công cụ.</li>\n' +
  '  <li>di chuyển giữa các mục trong đường dẫn phần tử của chân trang.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Các phím mũi tên <strong>Hướng xuống</strong> và <strong>Hướng lên</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>di chuyển giữa các mục menu trong menu.</li>\n' +
  '  <li>di chuyển giữa các mục trong menu thanh công cụ dạng bật lên.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Các phím <strong>mũi tên</strong> xoay vòng trong một phần UI tập trung.</p>\n' +
  '\n' +
  '<p>Để đóng một menu mở, một menu phụ đang mở, hoặc một menu dạng bật lên đang mở, hãy ấn phím <strong>Esc</strong>.</p>\n' +
  '\n' +
  '<p>Nếu trọng tâm hiện tại là ở phần “đầu” của một phần UI cụ thể, thao tác ấn phím <strong>Esc</strong> cũng sẽ thoát\n' +
  '  toàn bộ phần điều hướng bàn phím.</p>\n' +
  '\n' +
  '<h1>Thực hiện chức năng của một mục menu hoặc nút thanh công cụ</h1>\n' +
  '\n' +
  '<p>Khi mục menu hoặc nút thanh công cụ muốn dùng được tô sáng, hãy ấn <strong>Return</strong>, <strong>Enter</strong>,\n' +
  '  hoặc <strong>Phím cách</strong> để thực hiện chức năng mục đó.</p>\n' +
  '\n' +
  '<h1>Điều hướng giữa các hộp thoại không có nhiều tab</h1>\n' +
  '\n' +
  '<p>Trong các hộp thoại không có nhiều tab, khi hộp thoại mở ra, trọng tâm sẽ hướng vào thành phần tương tác đầu tiên.</p>\n' +
  '\n' +
  '<p>Di chuyển giữa các thành phần hộp thoại tương tác bằng cách ấn <strong>Tab</strong> hoặc <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<h1>Điều hướng giữa các hộp thoại có nhiều tab</h1>\n' +
  '\n' +
  '<p>Trong các hộp thoại có nhiều tab, khi hộp thoại mở ra, trọng tâm sẽ hướng vào nút đầu tiên trong menu tab.</p>\n' +
  '\n' +
  '<p>Di chuyển giữa các thành phần tương tác của tab hộp thoại này bằng cách ấn <strong>Tab</strong> hoặc\n' +
  '  <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>Chuyển sang một tab hộp thoại khác bằng cách chuyển trọng tâm vào menu tab, rồi ấn phím <strong>Mũi tên</strong> phù hợp\n' +
  '  để xoay vòng các tab hiện có.</p>\n');