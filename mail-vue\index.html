<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title></title>
    <link rel="icon" href="./src/assets/favicon.svg" type="image/svg+xml">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
</head>
<body>
<div id="loading-first">
    <div class="loading-icon">
        <svg class="circular" viewBox="0 0 20 20">
            <g class="loading-path">
                <circle r="3.375" class="dot1" rx="0" ry="0" />
                <circle r="3.375" class="dot2" rx="0" ry="0" />
                <circle r="3.375" class="dot4" rx="0" ry="0" />
                <circle r="3.375" class="dot3" rx="0" ry="0" />
            </g>
        </svg>
    </div>
</div>
<div id="app"></div>
<script type="module" src="/src/main.js"></script>
</body>
</html>
<style>
    #loading-first {
        position: fixed;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #fff;
        z-index: 500;
    }

    .loading-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 30px;
    }

    .circular {
        height: 30px;
        width: 30px;
        animation: loading-rotate 1s linear infinite;
    }

    .loading-path .dot1 {
        transform: translate(3.75px, 3.75px);
        fill: #409EFF;
        animation: custom-spin-move 1s infinite linear alternate;
        opacity: 0.3;
    }

    .loading-path .dot2 {
        transform: translate(calc(100% - 3.75px), 3.75px);
        fill: #409EFF;
        animation: custom-spin-move 1s infinite linear alternate;
        opacity: 0.3;
        animation-delay: 0.4s;
    }

    .loading-path .dot3 {
        transform: translate(3.75px, calc(100% - 3.75px));
        fill: #409EFF;
        animation: custom-spin-move 1s infinite linear alternate;
        opacity: 0.3;
        animation-delay: 1.2s;
    }

    .loading-path .dot4 {
        transform: translate(calc(100% - 3.75px), calc(100% - 3.75px));
        fill: #409EFF;
        animation: custom-spin-move 1s infinite linear alternate;
        opacity: 0.3;
        animation-delay: 0.8s;
    }

    @keyframes loading-rotate {
        to {
            transform: rotate(360deg);
        }
    }

    @keyframes custom-spin-move {
        to {
            opacity: 1;
        }
    }
</style>