/**
 * TinyMCE version 7.8.0 (TBD)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=e=>undefined===e;class r{constructor(e,t){this.tag=e,this.value=t}static some(e){return new r(!0,e)}static none(){return r.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?r.some(e(this.value)):r.none()}bind(e){return this.tag?e(this.value):r.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:r.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return null==e?r.none():r.some(e)}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}r.singletonNone=new r(!1);const n=e=>()=>e,s=n(!1),i=(e,t)=>((e,t,n)=>{for(let s=0,i=e.length;s<i;s++){const i=e[s];if(t(i,s))return r.some(i);if(n(i,s))break}return r.none()})(e,t,s);"undefined"!=typeof window?window:Function("return this;")();const o=()=>a(0,0),a=(e,t)=>({major:e,minor:t}),c={nu:a,detect:(e,t)=>{const r=String(t).toLowerCase();return 0===e.length?o():((e,t)=>{const r=((e,t)=>{for(let r=0;r<e.length;r++){const n=e[r];if(n.test(t))return n}})(e,t);if(!r)return{major:0,minor:0};const n=e=>Number(t.replace(r,"$"+e));return a(n(1),n(2))})(e,r)},unknown:o},u=(e,t)=>{const r=String(t).toLowerCase();return i(e,(e=>e.search(r)))},d=(e,r,n=0,s)=>{const i=e.indexOf(r,n);return-1!==i&&(!!t(s)||i+r.length<=s)},l=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,h=e=>t=>d(t,e),m=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>d(e,"edge/")&&d(e,"chrome")&&d(e,"safari")&&d(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,l],search:e=>d(e,"chrome")&&!d(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>d(e,"msie")||d(e,"trident")},{name:"Opera",versionRegexes:[l,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:h("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:h("firefox")},{name:"Safari",versionRegexes:[l,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(d(e,"safari")||d(e,"mobile/"))&&d(e,"applewebkit")}],v=[{name:"Windows",search:h("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>d(e,"iphone")||d(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:h("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:h("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:h("linux"),versionRegexes:[]},{name:"Solaris",search:h("sunos"),versionRegexes:[]},{name:"FreeBSD",search:h("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:h("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],g={browsers:n(m),oses:n(v)},p="Edge",w="Chromium",f="Opera",x="Firefox",S="Safari",y=e=>{const t=e.current,r=e.version,n=e=>()=>t===e;return{current:t,version:r,isEdge:n(p),isChromium:n(w),isIE:n("IE"),isOpera:n(f),isFirefox:n(x),isSafari:n(S)}},b=()=>y({current:void 0,version:c.unknown()}),O=y,R=(n(p),n(w),n("IE"),n(f),n(x),n(S),"Windows"),C="Android",A="Linux",k="macOS",D="Solaris",E="FreeBSD",I="ChromeOS",P=e=>{const t=e.current,r=e.version,n=e=>()=>t===e;return{current:t,version:r,isWindows:n(R),isiOS:n("iOS"),isAndroid:n(C),isMacOS:n(k),isLinux:n(A),isSolaris:n(D),isFreeBSD:n(E),isChromeOS:n(I)}},T=()=>P({current:void 0,version:c.unknown()}),_=P,B=(n(R),n("iOS"),n(C),n(A),n(k),n(D),n(E),n(I),(e,t,s)=>{const o=g.browsers(),a=g.oses(),d=t.bind((e=>((e,t)=>((e,t)=>{for(let r=0;r<e.length;r++){const n=t(e[r]);if(n.isSome())return n}return r.none()})(t.brands,(t=>{const r=t.brand.toLowerCase();return i(e,(e=>{var t;return r===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:c.nu(parseInt(t.version,10),0)})))})))(o,e))).orThunk((()=>((e,t)=>u(e,t).map((e=>{const r=c.detect(e.versionRegexes,t);return{current:e.name,version:r}})))(o,e))).fold(b,O),l=((e,t)=>u(e,t).map((e=>{const r=c.detect(e.versionRegexes,t);return{current:e.name,version:r}})))(a,e).fold(T,_),h=((e,t,r,s)=>{const i=e.isiOS()&&!0===/ipad/i.test(r),o=e.isiOS()&&!i,a=e.isiOS()||e.isAndroid(),c=a||s("(pointer:coarse)"),u=i||!o&&a&&s("(min-device-width:768px)"),d=o||a&&!u,l=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),h=!d&&!u&&!l;return{isiPad:n(i),isiPhone:n(o),isTablet:n(u),isPhone:n(d),isTouch:n(c),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:n(l),isDesktop:n(h)}})(l,d,e,s);return{browser:d,os:l,deviceType:h}}),L=e=>window.matchMedia(e).matches;let N=(e=>{let t,r=!1;return(...n)=>(r||(r=!0,t=e.apply(null,n)),t)})((()=>B(window.navigator.userAgent,r.from(window.navigator.userAgentData),L)));const F=()=>N();var M=tinymce.util.Tools.resolve("tinymce.util.Tools");const $=e=>t=>t.options.get(e),W=$("content_style"),U=$("content_css_cors"),K=$("body_class"),j=$("body_id"),V=e=>{const t=(e=>{var t;let r="";const n=e.dom.encode,s=null!==(t=W(e))&&void 0!==t?t:"";r+=`<base href="${n(e.documentBaseURI.getURI())}">`;const i=U(e)?' crossorigin="anonymous"':"";M.each(e.contentCSS,(t=>{r+='<link type="text/css" rel="stylesheet" href="'+n(e.documentBaseURI.toAbsolute(t))+'"'+i+">"})),s&&(r+='<style type="text/css">'+s+"</style>");const o=j(e),a=K(e),c=e.getBody().dir,u=c?' dir="'+n(c)+'"':"";return"<!DOCTYPE html><html><head>"+r+'</head><body id="'+n(o)+'" class="mce-content-body '+n(a)+'"'+u+">"+e.getContent()+(()=>{const e=F().os.isMacOS()||F().os.isiOS();return`<script>(${(e=>{document.addEventListener("click",(t=>{for(let r=t.target;r;r=r.parentNode)if("A"===r.nodeName){const n=r.getAttribute("href");if(n&&n.startsWith("#")){t.preventDefault();const e=document.getElementById(n.substring(1));return void(e&&e.scrollIntoView({behavior:"smooth"}))}(e?t.metaKey:t.ctrlKey&&!t.altKey)||t.preventDefault()}}),!1)}).toString()})(${e})<\/script>`})()+"</body></html>"})(e);e.windowManager.open({title:"Preview",size:"large",body:{type:"panel",items:[{name:"preview",type:"iframe",sandboxed:!0,transparent:!1}]},buttons:[{type:"cancel",name:"close",text:"Close",primary:!0}],initialData:{preview:t}}).focus("close")};e.add("preview",(e=>{(e=>{e.addCommand("mcePreview",(()=>{V(e)}))})(e),(e=>{const t=()=>e.execCommand("mcePreview");e.ui.registry.addButton("preview",{icon:"preview",tooltip:"Preview",onAction:t,context:"any"}),e.ui.registry.addMenuItem("preview",{icon:"preview",text:"Preview",onAction:t,context:"any"})})(e)}))}();