name = "cloud-mail"
main = "src/index.js"
compatibility_date = "2025-06-04"
keep_vars = true

[observability]
enabled = true

#[[d1_databases]]
#binding = "db"			#d1数据库绑定名默认不可修改
#database_name = ""		#d1数据库名字
#database_id = ""		#d1数据库id

#[[kv_namespaces]]
#binding = "kv"			#kv绑定名默认不可修改
#id = ""					#kv数据库id

#[[r2_buckets]]
#binding = "r2"			#r2对象存储绑定名默认不可修改
#bucket_name = ""		#r2对象存储桶的名字

[assets]
binding = "assets"		#静态资源绑定名默认不可修改
directory = "./dist"	#前端vue项目打包的静态资源存放位置,默认dist
not_found_handling = "single-page-application"
run_worker_first = true

[triggers]
crons = ["0 16 * * *"]	#定时任务每天晚上12点执行


#[vars]
#orm_log = false
#domain = []				#邮件域名可配置多个 示例: ["example1.com","example2.com"]
#admin = ""				#管理员的邮箱	示例: <EMAIL>
#jwt_secret = ""			#jwt令牌的密钥,随便填一串字符串
