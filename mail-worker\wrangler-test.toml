name = "cloud-mail-test"
main = "src/index.js"
compatibility_date = "2025-06-04"
keep_vars = true

[observability]
enabled = true
head_sampling_rate = 1

[[d1_databases]]
binding = "db"
database_name = "email-test"
database_id = ""

[[kv_namespaces]]
binding = "kv"
id = ""

[[r2_buckets]]
binding = "r2"
bucket_name = "email-test"

[assets]
binding = "assets"
directory = "./dist"
not_found_handling = "single-page-application"
run_worker_first = true

[triggers]
crons = ["0 16 * * *"]

[vars]
orm_log = true
domain = ["", ""]
admin = ""
jwt_secret = ""
