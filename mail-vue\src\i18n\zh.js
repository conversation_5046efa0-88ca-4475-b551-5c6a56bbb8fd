const zh = {
    inbox: '收件箱',
    drafts: '草稿箱',
    sent: '已发送',
    starred: '星标邮件',
    settings: '个人设置',
    analytics: '分析页',
    allUsers: '用户列表',
    allMail: '全部邮件',
    permissions: '权限控制',
    inviteCode: '注册密钥',
    SystemSettings: '系统设置',
    noMoreData: '没有更多数据了',
    noMessagesFound: '没有任何邮件',
    addAccount: '添加邮箱',
    emailAccount: '邮箱',
    deleteUser: '删除账户',
    deleteUserBtn: '删除账户',
    changePassword: '修改密码',
    newPassword: '新的密码',
    confirmPassword: '确认密码',
    add: '添加',
    manage: '管理',
    rename: '改名',
    delete: '删除',
    save: '保存',
    profile: '个人信息',
    change: '修改',
    changePwdBtn: '修改密码',
    username: '用户名',
    password: '密码',
    delAccount: '删除账户',
    delAccountMsg: '此操作将永久删除您的账户及其所有数据，无法恢复',
    totalReceived: '收件数量',
    totalSent: '发送数量',
    totalMailboxes: '邮箱数量',
    totalUsers: '用户数量',
    deleted: '删除',
    selectDeleted: '已删除',
    active: '正常',
    emailSource: '邮件来源',
    userGrowth: '用户增长',
    emailGrowth: '邮件增长',
    emailSent: '发送',
    emailReceived: '接收',
    sentToday: '今日发件',
    total: '次数',
    growthTotalUsers: '用户数',
    searchByEmail: '输入邮箱搜索',
    tabEmailAddress: '用户邮箱',
    tabReceived: '收件数量',
    tabSent: '发件数量',
    tabMailboxes: '邮箱数量',
    tabRegisteredAt: '注册时间',
    tabStatus: '状态',
    tabRole: '权限身份',
    roleName: '名字',
    role: '权限身份',
    all: '全部',
    normal: '正常',
    banned: '封禁',
    reset: '重置',
    restore: '恢复',
    tabSetting: '设置',
    registrationIp: '注册IP',
    recentIP: '近期IP',
    recentActivity: '近期活动',
    loginDevice: '登录设备',
    loginSystem: '登录系统',
    browserLogin: '登录浏览器',
    unauthorized: '无权限',
    unlimited: '无限制',
    sendCount: '邮件发送 :',
    accountCount: '邮箱添加 :',
    action: '操作',
    chgPwd: '改密',
    perm: '权限',
    btnBan: '禁用',
    admin: '超级管理员',
    addUser: '添加用户',
    select: '请选择',
    unknown: '未知',
    changePerm: '修改权限',
    from: '发件人',
    subject: '主题',
    sender: '发件人',
    user: '用户',
    searchByContent: '输入内容查询',
    noRecipient: '无收件人',
    received: '已接收',
    selectEmail: '邮箱',
    order: '排序',
    default: '默认',
    description: '描述',
    removeContent: '移除正文',
    removeAll: '丢弃邮件',
    expand: '展开',
    collapse: '收起',
    daily: '每天',
    searchRegKeyDesc: '输入注册码搜索',
    remainingUses: '剩余次数',
    exhausted: '已用尽',
    validUntil: '有效至期',
    expired: '已过期',
    copy: '复制',
    history: '记录',
    addRegKey: '添加注册码',
    regKey: '注册码',
    noCodeFound: '没有任何注册码',
    useHistory: '使用记录',
    date: '时间',
    roleDesc: '权限身份',
    noSubject: '无主题',
    recipient: '收件人',
    delivered: '发送成功',
    complained: '被标记为垃圾',
    delayed: '发送被延迟',
    bounced: '发送失败',
    attachments: '附件列表',
    attCount: '共 {total} 个',
    emailCount: '共 {total} 封',
    error404: '404错误, 找不到页面',
    home: '返回首页',
    loginBtn: '登录',
    regBtn: '注册',
    loginTitle: '输入账号信息以开始使用邮箱系统',
    regTitle: '输入账号密码以开始注册邮箱系统',
    confirmPwd: '确认密码',
    regKeyOptional: '注册码（可选）',
    noAccount: '还没有账号?',
    hasAccount: '已有账号?',
    regSwitch: '创建账号',
    loginSwitch: '去登录',
    websiteSetting: '网站设置',
    websiteReg: '用户注册',
    loginDomain: '登录框域名',
    multipleEmail: '多号模式',
    multipleEmailDesc: '开启后账号栏出现一个用户可以添加多个邮箱',
    physicallyWipeData: '物理清空数据',
    physicallyWipeDataDesc: '该操作会物理清空所有已被删除的数据',
    customization: '个性化设置',
    websiteTitle: '网站标题',
    loginBoxOpacity: '登录透明',
    loginBackground: '登录背景',
    emailSetting: '邮件设置',
    receiveEmail: '邮件接收',
    autoRefresh: '自动刷新',
    autoRefreshDesc: '轮询请求服务器获取最新邮件',
    sendEmail: '邮件发送',
    resendToken: '添加 Resend Token',
    R2OS: 'R2 对象存储',
    osDomain: '访问域名',
    emailPush: '邮件推送',
    tgBot: 'Telegram 机器人',
    disable: '关闭',
    disabled: '已关闭',
    otherEmail: '第三方邮箱',
    forwardingRules: '转发规则',
    forwardAll: '全部转发',
    rules: '规则转发',
    turnstileSetting: 'Turnstile 人机验证',
    signUpVerification: '注册验证',
    addEmailVerification: '添加验证',
    about: '关于',
    version: '版本',
    community: '交流',
    changeTitle: '修改标题',
    addResendTokenDesc: '输入内容添加，不填则删除',
    addOsDomain: '添加域名',
    domainDesc: '域名',
    addTurnstileSecret: '添加 Turnstile 密钥',
    backgroundTitle: '设置背景',
    tgBotDesc: '可以将接收的邮件转发到Tg机器人',
    tgBotToken: '机器人 token',
    toBotTokenDesc: '用户 chat_id 多个用,分开',
    otherEmailDesc: '可以将邮件转到其他服务商邮箱，需要在cloudflare验证邮箱',
    otherEmailInputDesc: '多个邮箱用, 分开',
    forwardingRulesDesc: '规则转发只会转发设置邮箱所接收的邮件',
    ruleEmailsInputDesc: '多个邮箱用, 分开',
    resendTokenList: 'Token 列表',
    domain: '域名',
    optional: '可选',
    subjectInputDesc: '请输入邮件主题',
    changeUserName: '修改用户名',
    sendSeparately: '分别发送',
    send: '发送',
    reply: '回复',
    confirm: '确定',
    cancel: '取消',
    delEmailConfirm: '确认删除该邮件吗？',
    delSuccessMsg: '删除成功',
    emptyEmailMsg: '邮箱不能为空',
    notEmailMsg: '输入的邮箱不合法',
    emptyPwdMsg: '密码不能为空',
    pwdLengthMsg: '密码最少六位',
    confirmPwdFailMsg: '两次密码输入不一致',
    emptyRegKeyMsg: '注册码不能为空',
    regSuccessMsg: '注册成功',
    copySuccessMsg: '复制成功',
    copyFailMsg: '复制失败',
    clearRegKey: '确认清除所有不可用的注册码？',
    clearSuccess: '清除成功',
    emptyRole: '身份类型不能为空',
    emptyTimeMsg: '有效时间不能为空',
    emptyCountMsg: '可用次数不能为空',
    addSuccessMsg: '添加成功',
    delConfirm: '确认删除{msg}吗?',
    emptyRoleNameMsg: '身份名不能为空',
    saveSuccessMsg: '保存成功',
    changeRoleTitle: '修改身份',
    addRoleTitle: '添加身份',
    emptyUserNameMsg: '用户名不能为空',
    delAccountConfirm: '确认删除当前账号及所有数据吗?',
    clearAllDelConfirm: '此操作不可逆转, 输入 <b style="font-weight: bold">确认删除</b> 继续操作',
    warning: '警告',
    delInputPattern: '确认删除',
    inputErrorMessage: '请输入确认删除',
    delBackgroundConfirm: '确定要删除这个背景吗?',
    enable: '启用',
    enabled: '已启用',
    reSendConfirm: '确认重置 {msg} 发件次数吗?',
    reSuccessMsg: '重置成功',
    restoreConfirm: '确认要恢复 {msg} 吗？',
    normalRestore: '普通恢复',
    allRestore: '包括已删除的数据',
    restoreSuccessMsg: '恢复成功',
    banRestore: '确认禁用 {msg} 吗？',
    logOut: '退出',
    clearContentConfirm: '确定要清空所有内容吗？',
    attLimitMsg: '附件大小限制28mb',
    emptyRecipientMsg: '收件人邮箱地址不能为空',
    emptySubjectMsg: '主题不能为空',
    emptyContentMsg: '邮件正文不能为空',
    noSeparateSendMsg: '分别发送暂时不支持附件',
    sendSuccessMsg: '发送成功',
    sendFailMsg: '发送失败',
    saveDraftConfirm: '是否保存草稿？',
    delEmailsConfirm: '确认批量删除这些邮件吗？',
    sending: '邮件正在发送中',
    sendingErrorMsg: '邮件正在发送中',
    networkErrorMsg: '网络错误，请检查网络连接',
    timeoutErrorMsg: '请求超时，请稍后重试',
    serverBusyErrorMsg: '服务器繁忙，请稍后重试',
    reqFailErrorMsg: '请求失败，请稍后再试',
    message: '邮件详情',
    language: '网站语言',
    totalUserAccount: '{msg} 个',
    sendBanned: '已禁用',
    wrote: '来信',
    support: '捐助',
    supportDesc: '请我喝杯奶茶',
    featDesc: '功能说明',
    emailInterception: '邮件拦截',
    emailInterceptionDesc: '输入邮箱或域名拦截邮件，可用于禁止用户接收某些网站的邮件',
    availableDomains: '可用域名',
    availableDomainsDesc: '限制用户只能使用指定的域名邮箱，不在配置名单内的域名会被禁止使用注册添加邮箱，接收发送邮件等功能，留空默认允许可用所有域名',
    backgroundUrlDesc: '在线图片链接',
    localUpload: '本地上传',
    imageLink: '图片链接',
    imageLinkErrorMsg: '图片链接不正确',
    backgroundWarning: '图片文件大小会影响网站加载速度',
    rulesVerify: '规则',
    rulesVerifyTitle: 'IP 每天使用 {count} 次后触发',
    botVerifyMsg: '请完成人机验证',
    noticeTitle: '网站公告',
    noticePopup: '登录弹窗',
    icon: '图标',
    position: '位置',
    offset: '偏移距离',
    duration: '显示时长',
    topRight: '右上',
    topLeft: '左上',
    bottomRight: '右下',
    bottomLeft: '左下',
    width: '宽度',
    titleDesc: '标题',
    noticeContentDesc: '公告内容，支持HTML',
    verifyModuleFailed: '人机验证模块加载失败，请刷新页面',
    popUp: '弹出',
    noRecipientTitle: '无人收件',
    noRecipientDesc: '即使没有注册的邮箱也能收到邮件',
    preview: '预览',
    help: '帮助',
    document: '项目文档'

}
export default zh
