const en = {
	IncorrectPwd: 'Incorrect password',
	addAccountDisabled: 'Add account feature is disabled',
	regDisabled: 'Sign up is disabled',
	emptyEmail: 'Email cannot be empty',
	notEmail: 'Invalid email',
	notExistDomain: 'Email domain does not exist',
	isDelAccount: 'This Email has been deleted',
	isRegAccount: 'This Email is already registered',
	accountLimit: 'Account limit reached',
	delMyAccount: 'Cannot delete your own account',
	noUserAccount: 'This email does not belong to the current user',
	usernameLengthLimit: 'Username length exceeds the limit',
	noOsDomainSendPic: 'Cannot send body images: R2 domain not configured',
	noOsSendPic: 'Cannot send body images: R2 object storage not configured',
	noOsDomainSendAtt: 'Cannot send attachments: R2 domain not configured',
	noOsSendAtt: 'Cannot send attachments: R2 object storage not configured',
	disabledSend: 'Email sending feature is disabled',
	noSeparateSend: 'Attachments are not supported in separate sending',
	daySendLimit: 'Daily send limit reached',
	totalSendLimit: 'Total send limit reached',
	daySendLack: 'Not enough remaining sends today',
	totalSendLack: 'Not enough total remaining sends',
	senderAccountNotExist: 'Sender email does not exist',
	noResendToken: 'Resend API token not configured',
	sendEmailNotCurUser: 'Sender email does not belong to current user',
	notExistEmailReply: 'Mail does not exist and cannot be replied to',
	pwdLengthLimit: 'Password length exceeds the limit',
	emailLengthLimit: 'Email length exceeds the limit',
	pwdMinLengthLimit: 'Password must be at least 6 characters',
	notEmailDomain: 'Invalid email domain',
	emptyRegKey: 'Invite code cannot be empty',
	notExistRegKey: 'Invite code does not exist',
	noRegKeyTotal: 'Invite code usage limit reached',
	regKeyExpire: 'Invite code has expired',
	emailAndPwdEmpty: 'Email and password cannot be empty',
	notExistUser: 'Email does not exist',
	isDelUser: 'This email has been deleted',
	isBanUser: 'This email has been banned',
	regKeyUseCount: 'Usage count cannot be empty',
	emptyRegKeyExpire: 'Valid until time cannot be empty',
	isExistRegKye: 'Invite code already exists',
	roleNotExist: 'Role does not exist',
	emptyRoleName: 'Role name cannot be empty',
	roleNameExist: 'Role name already exists',
	delDefRole: 'Default role cannot be deleted',
	notJsonDomain: 'Environment variable "domain" must be in JSON format',
	noOsUpBack: 'Cannot upload background: R2 object storage not configured',
	noOsDomainUpBack: 'Cannot upload background: R2 domain not configured',
	starNotExistEmail: 'Starred email does not exist',
	emptyBotToken: 'Please verify that you are human',
	botVerifyFail: 'Bot verification failed, please try again',
	authExpired: 'Authentication has expired. Please sign in again',
	unauthorized: 'Unauthorized',
	bannedSend: 'You are banned from sending emails',
	initSuccess: 'Successfully initialized',
	noDomainPermAdd: "No permission to add this domain email",
	noDomainPermReg: "No permission to register this domain email",
	noDomainPermRegKey: "Registration code not valid for this domain",
	noDomainPermSend: "No permission to send from this domain email",
	JWTMismatch: 'JWT secret mismatch',
	publicTokenFail: 'Token validation failed',
	notAdmin: 'The entered email is not an administrator email',
	emailExistDatabase: 'Email already exists in the database',
	perms: {
		"邮件": "Email",
		"邮件发送": "Send Email",
		"邮件删除": "Delete Email",
		"邮箱侧栏": "Account",
		"邮箱查看": "View Account",
		"邮箱添加": "Add Account",
		"邮箱删除": "Delete Account",
		"个人设置": "Settings",
		"用户注销": "Delete User",
		"分析页": "Analytics",
		"数据查看": "View Data",
		"用户信息": "All Users",
		"用户查看": "View User",
		"用户添加": "Add User",
		"密码修改": "Change Password",
		"状态修改": "Change Status",
		"权限修改": "Change Role",
		"用户删除": "Delete User",
		"邮件列表": "All Mail",
		"邮件查看": "View Email",
		"权限控制": "Role",
		"身份添加": "Add Role",
		"身份查看": "View Role",
		"身份修改": "Change Role",
		"身份删除": "Delete Role",
		"注册密钥": "Invite Code",
		"密钥查看": "View Code",
		"密钥添加": "Add Code",
		"密钥删除": "Delete Code",
		"系统设置": "System Settings",
		"设置查看": "View Settings",
		"设置修改": "Change Settings",
		"物理清空": "Physical Purge",
		"发件重置": "Reset Send Count"
	}
};

export default en;
