/**
 * TinyMCE version 7.8.0 (TBD)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");let t=0;var o=tinymce.util.Tools.resolve("tinymce.util.Delay");const n=e=>{e.on("PreInit",(()=>{e.queryCommandSupported("QuickbarInsertImage")||e.addCommand("QuickbarInsertImage",(()=>{(e=>new Promise((t=>{let n=!1;const r=document.createElement("input");r.type="file",r.accept="image/*",r.style.position="fixed",r.style.left="0",r.style.top="0",r.style.opacity="0.001",document.body.appendChild(r);const i=e=>{var o;n||(null===(o=r.parentNode)||void 0===o||o.removeChild(r),n=!0,t(e))},s=e=>{i(Array.prototype.slice.call(e.target.files))};r.addEventListener("input",s),r.addEventListener("change",s);const a=t=>{const r=()=>{i([])};n||("focusin"===t.type?o.setEditorTimeout(e,r,1e3):r()),e.off("focusin remove",a)};e.on("focusin remove",a),r.click()})))(e).then((o=>{if(o.length>0){const n=o[0];(e=>new Promise((t=>{const o=new FileReader;o.onloadend=()=>{t(o.result.split(",")[1])},o.readAsDataURL(e)})))(n).then((o=>{((e,o,n)=>{const r=e.editorUpload.blobCache,i=r.create((e=>{const o=(new Date).getTime(),n=Math.floor(window.crypto.getRandomValues(new Uint32Array(1))[0]/4294967295*1e9);return t++,e+"_"+n+t+String(o)})("mceu"),n,o);r.add(i),e.insertContent(e.dom.createHTML("img",{src:i.blobUri()}))})(e,o,n)}))}}))}))}))},r=e=>t=>typeof t===e,i=e=>"string"===(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(o=n=e,(r=String).prototype.isPrototypeOf(o)||(null===(i=n.constructor)||void 0===i?void 0:i.name)===r.name)?"string":t;var o,n,r,i})(e);const s=r("boolean"),a=r("function"),l=e=>t=>t.options.get(e),c=l("quickbars_selection_toolbar"),u=l("quickbars_insert_toolbar"),d=l("quickbars_image_toolbar"),m=()=>false;class g{constructor(e,t){this.tag=e,this.value=t}static some(e){return new g(!0,e)}static none(){return g.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?g.some(e(this.value)):g.none()}bind(e){return this.tag?e(this.value):g.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:g.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return null==e?g.none():g.some(e)}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}g.singletonNone=new g(!1),"undefined"!=typeof window?window:Function("return this;")();var h=(e,t,o,n,r)=>e(o,n)?g.some(o):a(r)&&r(o)?g.none():t(o,n,r);const b=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},p=b,v=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},f=(e,t,o)=>{let n=e.dom;const r=a(o)?o:m;for(;n.parentNode;){n=n.parentNode;const e=p(n);if(t(e))return g.some(e);if(r(e))break}return g.none()},y=(e,t,o)=>f(e,(e=>v(e,t)),o),k=e=>{const t=u(e);t.length>0&&e.ui.registry.addContextToolbar("quickblock",{predicate:t=>{const o=p(t),n=e.schema.getTextBlockElements(),r=t=>t.dom===e.getBody();return!(e=>{const t=e.dom;return!(!t||!t.hasAttribute)&&t.hasAttribute("data-mce-bogus")})(o)&&((e,t,o)=>h(((e,t)=>v(e,t)),y,e,'table,[data-mce-bogus="all"]',o))(o,0,r).fold((()=>((e,t,o)=>((e,t,o)=>h(((e,t)=>t(e)),f,e,t,o))(e,t,o).isSome())(o,(t=>t.dom.nodeName.toLowerCase()in n&&e.dom.isEmpty(t.dom)),r)),m)},items:t,position:"line",scope:"editor"})};e.add("quickbars",(e=>{(e=>{const t=e.options.register,o=e=>t=>{const o=s(t)||i(t);return o?s(t)?{value:t?e:"",valid:o}:{value:t.trim(),valid:o}:{valid:!1,message:"Must be a boolean or string."}},n="bold italic | quicklink h2 h3 blockquote";t("quickbars_selection_toolbar",{processor:o(n),default:n});const r="quickimage quicktable";t("quickbars_insert_toolbar",{processor:o(r),default:r});const a="alignleft aligncenter alignright";t("quickbars_image_toolbar",{processor:o(a),default:a})})(e),n(e),(e=>{e.ui.registry.addButton("quickimage",{icon:"image",tooltip:"Insert image",onAction:()=>e.execCommand("QuickbarInsertImage")}),e.ui.registry.addButton("quicktable",{icon:"table",tooltip:"Insert table",onAction:()=>{(e=>{e.execCommand("mceInsertTable",!1,{rows:2,columns:2})})(e)}})})(e),k(e),(e=>{const t=t=>e.dom.isEditable(t),o=e=>{const o="FIGURE"===e.nodeName&&/image/i.test(e.className),n="IMG"===e.nodeName||o,r=(e=>void 0!==e.dom.classList)(i=p(e))&&i.dom.classList.contains("mce-pagebreak");var i;return n&&t(e.parentElement)&&!r},n=d(e);n.length>0&&e.ui.registry.addContextToolbar("imageselection",{predicate:o,items:n,position:"node"});const r=c(e);r.length>0&&e.ui.registry.addContextToolbar("textselection",{predicate:n=>!o(n)&&!e.selection.isCollapsed()&&t(n),items:r,position:"selection",scope:"editor"})})(e)}))}();